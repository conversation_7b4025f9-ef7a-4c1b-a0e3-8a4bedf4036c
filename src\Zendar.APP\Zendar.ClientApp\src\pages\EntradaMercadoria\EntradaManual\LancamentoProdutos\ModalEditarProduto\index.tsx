import {
  ModalProps,
  ModalContent,
  ModalBody,
  useDisclosure,
  ModalHeader,
  Text,
  ModalFooter,
  HStack,
  Button,
  GridItem,
  Flex,
  ModalCloseButton,
} from '@chakra-ui/react';
import React, { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import { SimpleCard } from 'components/update/Form/SimpleCard';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import { NumberInput } from 'components/update/Input/NumberInput';

import { yupResolver, FormData } from './validationForm';

type ProdutoResponse = {
  quantidade: number;
  valorUnitarioEntrada: number;
  valorIpi: number;
  valorIcmsSt: number;
  valorFcpSt: number;
  custoAdicional: number;
  valorTotal: number;
};

type ModalEditarProdutoResponse = {
  produto: ProdutoResponse;
};

type ModalEditarProdutoProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> &
  InstanceProps<ModalEditarProdutoResponse> & {
    nomeProduto: string;
    produtoId: string;
    produtoTipoVariacao: boolean;
    variacoes: {
      cor: string | null;
      corId: string | null;
      nome: string;
      produtoCorTamanhoId: string;
      produtoId: string;
      quantidade: number;
      tamanho: string | null;
      tamanhoId: string | null;
    }[];
    cores: { id: string; nome: string }[];
    tamanhos: { id: string; nome: string }[];

    dadosEntrada: FormData;

    volumeUnitario?: boolean;
    entradaRateiaIcmsSt: boolean;

    casasDecimaisQuantidade: number;
    casasDecimaisValor: number;
  };

export const ModalEditarProduto = create<
  ModalEditarProdutoProps,
  ModalEditarProdutoResponse
>(
  ({
    onResolve,
    onReject,
    dadosEntrada,
    casasDecimaisQuantidade,
    casasDecimaisValor,
    volumeUnitario = false,
    nomeProduto,
    entradaRateiaIcmsSt,
    ...rest
  }) => {
    const formMethods = useForm<FormData>({
      resolver: yupResolver,
      defaultValues: dadosEntrada,
    });

    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = formMethods.handleSubmit(async (data) => {
      setIsLoading(true);

      const produtoResponse: ProdutoResponse = {
        quantidade: data.quantidade,
        valorUnitarioEntrada: data.valorUnitario || 0,
        valorIpi: data.ipi || 0,
        valorIcmsSt: entradaRateiaIcmsSt ? 0 : data.icmsSt || 0,
        valorFcpSt: data.fcpSt || 0,
        custoAdicional: data.custoAdicional || 0,
        valorTotal: data.quantidade * data.valorUnitario || 0,
      };

      onResolve({ produto: produtoResponse });
    });

    function handleCancelar() {
      onReject();
    }

    return (
      <ModalPadraoChakra
        isCentered
        size="full"
        {...rest}
        isOpen={isOpen}
        onClose={onClose}
      >
        <ModalContent h="unset" bg="gray.100" borderRadius="0px">
          {isLoading && <LoadingPadrao />}
          <ModalHeader
            px="40px"
            py="20px"
            color="violet.500"
            fontWeight="normal"
            fontSize="18px"
          >
            Editar produto
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody px="40px" pt="0px">
            <FormProvider {...formMethods}>
              <SimpleCard
                boxShadow="none"
                bg="violet.500"
                p="16px"
                as={HStack}
                mb="24px"
              >
                <Flex flexDir="column" w="50%">
                  <Text color="white" fontSize="12px">
                    Produto:
                  </Text>
                  <Text
                    color="secondary.300"
                    fontSize="sm"
                    fontWeight="semibold"
                  >
                    {nomeProduto}
                  </Text>
                </Flex>
              </SimpleCard>
              <SimpleCard
                boxShadow="none"
                bg="gray.50"
                border="1px solid #CCCCCC"
              >
                <SimpleGridForm>
                  <NumberInput
                    id="quantidade"
                    name="quantidade"
                    label="Quantidade"
                    pl="12px"
                    textAlign="left"
                    placeholder={
                      volumeUnitario
                        ? '0'
                        : `0,${'0'.repeat(casasDecimaisQuantidade)}`
                    }
                    scale={volumeUnitario ? 0 : casasDecimaisQuantidade}
                    colSpan={3}
                  />

                  <NumberInput
                    id="valorUnitario"
                    name="valorUnitario"
                    label="Valor unitário"
                    bgLeftElement="gray.50"
                    editarFundoLeftElemento
                    leftElement="R$"
                    leftElementFontSize="xs"
                    placeholder={`0,${'0'.repeat(casasDecimaisValor)}`}
                    scale={casasDecimaisValor}
                    colSpan={3}
                  />
                  <GridItem colSpan={6} />

                  <NumberInput
                    id="ipi"
                    name="ipi"
                    bgLeftElement="gray.50"
                    editarFundoLeftElemento
                    label="IPI"
                    leftElement="R$"
                    leftElementFontSize="xs"
                    placeholder={`0,${'0'.repeat(casasDecimaisValor)}`}
                    scale={casasDecimaisValor}
                    colSpan={3}
                  />
                  <NumberInput
                    id="icmsSt"
                    name="icmsSt"
                    bgLeftElement="gray.50"
                    editarFundoLeftElemento
                    label="ICMS ST"
                    leftElement="R$"
                    leftElementFontSize="xs"
                    placeholder={`0,${'0'.repeat(casasDecimaisValor)}`}
                    scale={casasDecimaisValor}
                    colSpan={3}
                    isDisabled={entradaRateiaIcmsSt}
                  />
                  <NumberInput
                    id="fcpSt"
                    name="fcpSt"
                    bgLeftElement="gray.50"
                    editarFundoLeftElemento
                    label="FCP ST"
                    leftElement="R$"
                    leftElementFontSize="xs"
                    placeholder={`0,${'0'.repeat(casasDecimaisValor)}`}
                    scale={casasDecimaisValor}
                    colSpan={3}
                  />

                  <NumberInput
                    id="custoAdicional"
                    name="custoAdicional"
                    bgLeftElement="gray.50"
                    editarFundoLeftElemento
                    leftElementFontSize="xs"
                    label="Custo adicional"
                    leftElement="R$"
                    helperText="Os valores deste campo não serão somados ao valor total da entrada, servindo apenas para compor o custo do produto."
                    placeholder={`0,${'0'.repeat(casasDecimaisValor)}`}
                    scale={casasDecimaisValor}
                    colSpan={3}
                  />
                </SimpleGridForm>
              </SimpleCard>
            </FormProvider>
          </ModalBody>
          <ModalFooter
            p="16px 32px"
            borderTop="1px"
            gap="24px"
            position="sticky"
            borderColor="purple.500"
            mx={{ base: 0, md: 8 }}
          >
            <Button
              colorScheme="gray"
              variant="outlineDefault"
              onClick={handleCancelar}
              borderRadius="full"
              minW="160px"
              height="32px"
              fontSize="14px"
              fontWeight="600"
              w={['full', 'full', '160px']}
            >
              Cancelar
            </Button>
            <Button
              colorScheme="secondary"
              borderRadius="full"
              w={['full', 'full', '160px']}
              height="32px"
              fontSize="14px"
              fontWeight="600"
              onClick={handleSubmit}
            >
              Confirmar
            </Button>
          </ModalFooter>
        </ModalContent>
      </ModalPadraoChakra>
    );
  }
);
