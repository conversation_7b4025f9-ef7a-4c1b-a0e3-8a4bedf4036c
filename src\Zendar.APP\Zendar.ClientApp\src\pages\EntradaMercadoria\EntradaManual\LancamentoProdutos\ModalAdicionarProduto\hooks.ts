import { useCallback, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';
import {
  checarPesquisaPorCodigoBarras,
  checarPesquisaPorGtinEan,
  checarPesquisaPorSKU,
} from 'helpers/validation/checarCodigo';

import api, { ResponseApi } from 'services/api';

import { ModalCriarVariacao } from 'pages/EntradaMercadoria/Importacao/Continuar/VincularProdutos/ModalVincularProduto/components/ModalCriarVariacao';
import { ModalCadastrarCor } from 'pages/EntradaMercadoria/ModalCadastrarCor';
import { ModalCadastrarProduto } from 'pages/EntradaMercadoria/ModalCadastrarProduto';
import { ModalCadastrarTamanho } from 'pages/EntradaMercadoria/ModalCadastrarTamanho';

import { GridPaginadaRetorno } from 'components/Grid/Paginacao';
import { ModalGradeTamanhos } from 'components/update/Modal/ModalGradeTamanhos';
import { PaginationData } from 'components/update/Pagination';

import OptionType from 'types/optionType';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import StatusConsultaEnum from 'constants/enum/statusConsulta';
import TipoProdutoEnum from 'constants/enum/tipoProduto';
import ConstanteFuncionalidades from 'constants/permissoes';

import {
  Lojas,
  ProdutoOptionProps,
  FormData,
  TamanhoQuantidade,
  ProdutoResponse,
  ModalAdicionarProdutoResponse,
  EntradaMercadoriaAdicionarItens,
  TipoCadastro,
  ProdutoCoresProps,
  ProdutoTamanhoProps,
  NewOption,
  Produto,
  Variacao,
} from './validationForm';

export const useModalAdicionarProduto = (
  formMethods: UseFormReturn<FormData>,
  casasDecimaisQuantidade: number,
  adicionarProduto: (newProduto: ProdutoResponse) => Promise<void>,
  entradaRateiaIcmsSt: boolean,
  onResolve: (result?: ModalAdicionarProdutoResponse | undefined) => void,
  fecharModal: () => void
) => {
  const [isLoadingProduto, setIsLoadingProduto] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [listaVariacoes, setListaVariacoes] = useState<
    EntradaMercadoriaAdicionarItens[]
  >([]);

  const { watch, setValue, setFocus } = formMethods;
  const {
    listaTamanhoIdQuantidade,
    cor: corWatch,
    produto: produtoWatch,
    pesquisarPorLeitor: pesquisarPorLeitorWatch,
  } = watch();

  const { permitido: temPermissaoCadastrarProduto } = auth.possuiPermissao(
    ConstanteFuncionalidades.PRODUTO_CADASTRAR
  );
  const { permitido: temPermissaoCadastrarCor } = auth.possuiPermissao(
    ConstanteFuncionalidades.COR_CADASTRAR
  );
  const { permitido: temPermissaoCadastrarTamanho } = auth.possuiPermissao(
    ConstanteFuncionalidades.TAMANHO_CADASTRAR
  );

  const produtoSelecionado = produtoWatch?.value;

  const coresDoProduto = produtoWatch?.value?.coresOptions || [];
  const tamanhosDoProduto = produtoWatch?.value?.tamanhosOptions || [];

  const produtoTipoSimples =
    produtoWatch?.value?.tipoProduto === TipoProdutoEnum.PRODUTO_SIMPLES;
  const produtoTipoVariacao =
    produtoWatch?.value?.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;
  const produtoDeVolumeUnitario = produtoWatch?.value?.volumeUnitario;
  const produtoTemCores = coresDoProduto?.length > 0;
  const produtoTemTamanhos = tamanhosDoProduto?.length > 0;

  const corEscolhida = corWatch;

  const tamanhoEscolhido =
    (listaTamanhoIdQuantidade || [])?.length > 0 &&
    listaTamanhoIdQuantidade &&
    listaTamanhoIdQuantidade[0]?.tamanho;

  const podeConfirmar = useCallback(() => {
    if (produtoTipoSimples) {
      return true;
    }

    if (produtoTemCores && produtoTemTamanhos) {
      return !!corEscolhida && !!tamanhoEscolhido;
    }

    if (produtoTemCores) {
      return !!corEscolhida;
    }

    if (produtoTemTamanhos) {
      return !!tamanhoEscolhido;
    }

    return false;
  }, [
    corEscolhida,
    produtoTemCores,
    produtoTemTamanhos,
    produtoTipoSimples,
    tamanhoEscolhido,
  ])();

  const handleFocus = useCallback(
    async (
      input:
        | 'valorUnitario'
        | 'listaTamanhoIdQuantidade.0.quantidade'
        | 'cor'
        | 'listaTamanhoIdQuantidade.0.tamanho'
        | 'produto'
    ) => {
      setIsLoading(true);

      await new Promise((resolve) => {
        setTimeout(() => {
          formMethods.setFocus(input);
          resolve(null);
        }, 1000);
      });

      setIsLoading(false);
    },
    [formMethods]
  );

  const temGradeLancada = (listaTamanhoIdQuantidade || []).length > 1;

  const limparCampoProduto = useCallback(() => {
    setValue('produto', null);
  }, [setValue]);

  const resetarQuantidadeTamanho = useCallback(() => {
    setValue('listaTamanhoIdQuantidade.0.quantidade', 1);
    setValue('listaTamanhoIdQuantidade.0.tamanho', null);
    setValue('listaTamanhoIdQuantidade', []);
  }, [setValue]);

  const limparCamposValoresFiscais = useCallback(() => {
    setValue('ipi', 0);
    setValue('icmsSt', 0);
    setValue('fcpSt', 0);
    setValue('custoAdicional', 0);
  }, [setValue]);

  const limparValoresFiscaisVoltarValorPrecoCompra = useCallback(() => {
    setValue('ipi', 0);
    setValue('icmsSt', 0);
    setValue('fcpSt', 0);
    setValue('custoAdicional', 0);
    setValue('valorUnitario', produtoWatch?.value?.precoCompra || 0);
  }, []);

  const quantidadeMaiorQueZero = () => {
    const listaProdutos = watch('listaTamanhoIdQuantidade') || [];
    const isQuantidadeValida = listaProdutos?.every(
      (element) => element.quantidade > 0
    );
    if (isQuantidadeValida) return true;

    return false;
  };

  const handleAbrirModalEscolherGradeTamanhos = async () => {
    if (produtoTemTamanhos) {
      const tamanhos = tamanhosDoProduto?.map((tamanho) => {
        const tamanhoQuantidade = (listaTamanhoIdQuantidade || []).find(
          (tamanhoIdQuantidade) =>
            tamanhoIdQuantidade.tamanho?.value === tamanho.value
        );

        return {
          produtoCorTamanhoId: tamanho.value,
          tamanho: tamanho.label,
          padraoSistema: false,
          quantidade: tamanhoQuantidade?.quantidade || 0,
        };
      });
      try {
        const tamanhosInformadosNoModalGrade = await ModalGradeTamanhos({
          produtoNome: produtoWatch?.label || '',
          corDescricao: corEscolhida?.label,
          casasDecimaisQuantidade,
          volumeUnitario: produtoDeVolumeUnitario,
          tamanhos,
        });

        const novaListaTamanhosIdQuantidade =
          tamanhosInformadosNoModalGrade?.length > 0
            ? tamanhosInformadosNoModalGrade?.map(
                (tamanho) =>
                  ({
                    quantidade: tamanho.quantidade,
                    tamanho: {
                      value: tamanho.produtoCorTamanhoId,
                      label: tamanho.tamanhoDescricao,
                    },
                  } as TamanhoQuantidade)
              )
            : [];

        if (tamanhosInformadosNoModalGrade?.length === 1) {
          const unicoTamanhoInformado = tamanhosInformadosNoModalGrade[0];

          setValue('listaTamanhoIdQuantidade.0.tamanho', {
            value: unicoTamanhoInformado?.produtoCorTamanhoId,
            label: unicoTamanhoInformado?.tamanhoDescricao || '',
          });
          setValue(
            'listaTamanhoIdQuantidade.0.quantidade',
            unicoTamanhoInformado.quantidade
          );
        }

        setValue('listaTamanhoIdQuantidade', novaListaTamanhosIdQuantidade);
        return true;
      } catch (error) {
        return false;
      }
    }
    return false;
  };

  const adicionarVariacaoNaLista = useCallback(() => {
    const data: FormData = watch();
    const produto: EntradaMercadoriaAdicionarItens = {
      id: data.produto?.value.id || '',
      produtoId: data.produto?.value.id || '',
      corId: data.cor?.value || '',
      corDescricao: data.cor?.label || '',
      listaTamanhoIdQuantidade:
        data.listaTamanhoIdQuantidade?.map((item) => ({
          id: item.tamanho?.value || '',
          tamanhoDescricao: item.tamanho?.label || '',
          quantidade: item.quantidade,
        })) || [],
      valorUnitarioEntrada: data.valorUnitario,
      valorIpi: data.ipi,
      valorIcmsSt: data.icmsSt,
      valorFcpSt: data.fcpSt,
      custoAdicional: data.custoAdicional,
    };

    if (produtoTemCores && !produtoTemTamanhos) {
      setListaVariacoes((prev) => {
        const variacaoCorJaAdicionada = prev.find(
          (item) => item.corId === produto.corId
        );

        if (variacaoCorJaAdicionada) {
          return prev.map((item) =>
            item.corId === produto.corId
              ? {
                  ...item,
                  listaTamanhoIdQuantidade: [
                    {
                      id: item.listaTamanhoIdQuantidade[0].id,
                      quantidade:
                        item.listaTamanhoIdQuantidade[0].quantidade +
                        produto.listaTamanhoIdQuantidade[0].quantidade,
                      tamanhoDescricao:
                        item.listaTamanhoIdQuantidade[0].tamanhoDescricao,
                    },
                  ],
                }
              : item
          );
        }

        return [...prev, produto];
      });
      resetarQuantidadeTamanho();
      setValue('cor', null);
      setFocus('cor');
      return;
    }

    if (!produtoTemCores && produtoTemTamanhos) {
      setListaVariacoes((prev) => {
        const variacaoTamanhoJaAdicionada = prev.find((item) =>
          item.listaTamanhoIdQuantidade.find(
            (tamanho) => tamanho.id === produto.listaTamanhoIdQuantidade[0].id
          )
        );

        if (variacaoTamanhoJaAdicionada) {
          return prev.map((item) =>
            item.listaTamanhoIdQuantidade.find(
              (tamanho) => tamanho.id === produto.listaTamanhoIdQuantidade[0].id
            )
              ? {
                  ...item,
                  listaTamanhoIdQuantidade: [
                    {
                      id: item.listaTamanhoIdQuantidade[0].id,
                      quantidade:
                        item.listaTamanhoIdQuantidade[0].quantidade +
                        produto.listaTamanhoIdQuantidade[0].quantidade,
                      tamanhoDescricao:
                        item.listaTamanhoIdQuantidade[0].tamanhoDescricao,
                    },
                  ],
                }
              : item
          );
        }

        return [...prev, produto];
      });
      resetarQuantidadeTamanho();
      setValue('listaTamanhoIdQuantidade.0.tamanho', null);
      setFocus('listaTamanhoIdQuantidade.0.tamanho');
      return;
    }

    setListaVariacoes((prev) => {
      const variacaoCorJaAdicionada = prev.find(
        (item) => item.corId === produto.corId
      );

      const variacaoTamanhoJaAdicionada = prev.find((item) => {
        if (item.corId === produto.corId) {
          return item.listaTamanhoIdQuantidade.find(
            (tamanho) => tamanho.id === produto.listaTamanhoIdQuantidade[0].id
          );
        }
        return false;
      });

      if (variacaoCorJaAdicionada && variacaoTamanhoJaAdicionada) {
        return prev.map((item) => {
          if (item.corId === produto.corId) {
            return {
              ...item,
              listaTamanhoIdQuantidade: item.listaTamanhoIdQuantidade.map(
                (tamanho) => ({
                  ...tamanho,
                  quantidade:
                    tamanho.id === produto.listaTamanhoIdQuantidade[0].id
                      ? tamanho.quantidade +
                        produto.listaTamanhoIdQuantidade[0].quantidade
                      : tamanho.quantidade,
                })
              ),
            };
          }

          return item;
        });
      }

      if (variacaoCorJaAdicionada && !variacaoTamanhoJaAdicionada) {
        return prev.map((item) => {
          if (item.corId === produto.corId) {
            return {
              ...item,
              listaTamanhoIdQuantidade: [
                ...item.listaTamanhoIdQuantidade,
                ...produto.listaTamanhoIdQuantidade,
              ],
            };
          }
          return item;
        });
      }

      return [...prev, produto];
    });
    resetarQuantidadeTamanho();
    setFocus('cor');
  }, [
    watch,
    produtoTemCores,
    produtoTemTamanhos,
    resetarQuantidadeTamanho,
    setValue,
    setFocus,
  ]);

  function adapterVariacoes(data: Produto[]): Variacao[] {
    return data.flatMap((item) =>
      item.listaTamanhoIdQuantidade.map(
        (tamanho: { tamanhoId: string | null; quantidade: number }) => ({
          corId: item.corId,
          tamanhoId: tamanho.tamanhoId,
          quantidade: tamanho.quantidade,
        })
      )
    );
  }

  const adicionarUmProdutoSimples = useCallback(
    async (data: FormData, confirmarAdicionarOutro: boolean) => {
      const produtoParaAdicionar = {
        produtoId: data?.produto?.value?.id || '',
        variacoes: [
          {
            corId: null,
            tamanhoId: null,
            quantidade: data?.listaTamanhoIdQuantidade?.[0]?.quantidade || 0,
          },
        ],
        valorUnitarioEntrada: data.valorUnitario || 0,
        valorIpi: data.ipi || 0,
        valorIcmsSt: entradaRateiaIcmsSt ? 0 : data.icmsSt || 0,
        valorFcpSt: data.fcpSt || 0,
        custoAdicional: data.custoAdicional || 0,
      };
      if (confirmarAdicionarOutro) {
        await adicionarProduto(produtoParaAdicionar);
        onResolve({
          produto: null,
          confirmarAdicionarOutro: true,
        });
      } else {
        onResolve({
          produto: produtoParaAdicionar,
          confirmarAdicionarOutro: false,
        });
      }
    },
    [adicionarProduto, entradaRateiaIcmsSt, onResolve]
  );

  const adicionarUmProdutoDoTipoVariacao = useCallback(
    async (data: FormData, confirmarAdicionarOutro: boolean) => {
      const produtoParaAdicionar: ProdutoResponse = {
        produtoId: data?.produto?.value.id || '',
        variacoes: data.listaTamanhoIdQuantidade?.map((tamanhoQuantidade) => ({
          tamanhoId: tamanhoQuantidade.tamanho?.value || null,
          quantidade: tamanhoQuantidade.quantidade,
          corId: data.cor?.value || null,
        })),
        valorUnitarioEntrada: data.valorUnitario || 0,
        valorIpi: data.ipi || 0,
        valorIcmsSt: entradaRateiaIcmsSt ? 0 : data.icmsSt || 0,
        valorFcpSt: data.fcpSt || 0,
        custoAdicional: data.custoAdicional || 0,
      };
      if (confirmarAdicionarOutro) {
        await adicionarProduto(produtoParaAdicionar);
        onResolve({
          produto: null,
          confirmarAdicionarOutro: true,
        });
      } else {
        onResolve({
          produto: produtoParaAdicionar,
          confirmarAdicionarOutro: false,
        });
      }
    },
    [adicionarProduto, entradaRateiaIcmsSt, onResolve]
  );

  const adicionarUmaListaProdutosDoTipoVariacao = useCallback(
    async (data: FormData, confirmarAdicionarOutro: boolean) => {
      const informacoesProdutoSendoAdicionado = listaVariacoes.map(
        (variacaoCorTamanho) => ({
          produtoId: variacaoCorTamanho.produtoId,
          corId: (variacaoCorTamanho.corId || null) as string,
          listaTamanhoIdQuantidade:
            variacaoCorTamanho.listaTamanhoIdQuantidade?.map(
              (tamanhoQuantidade) => ({
                tamanhoId: tamanhoQuantidade.id || null,
                quantidade: tamanhoQuantidade.quantidade,
              })
            ),
          valorUnitarioEntrada: variacaoCorTamanho.valorUnitarioEntrada,
          valorIpi: variacaoCorTamanho.valorIpi,
          valorIcmsSt: entradaRateiaIcmsSt ? 0 : variacaoCorTamanho.valorIcmsSt,
          valorFcpSt: variacaoCorTamanho.valorFcpSt,
          custoAdicional: variacaoCorTamanho.custoAdicional,
        })
      );
      const variacoes = adapterVariacoes(informacoesProdutoSendoAdicionado);

      const produtosParaAdicionar: ProdutoResponse = {
        produtoId: data?.produto?.value?.id ?? '',
        variacoes,
        valorUnitarioEntrada: data?.valorUnitario ?? 0,
        valorIpi: data?.ipi ?? 0,
        valorIcmsSt: entradaRateiaIcmsSt ? 0 : data?.icmsSt ?? 0,
        valorFcpSt: data?.fcpSt ?? 0,
        custoAdicional: data?.custoAdicional ?? 0,
      };
      await adicionarProduto(produtosParaAdicionar);
      if (confirmarAdicionarOutro) {
        onResolve({
          produto: null,
          confirmarAdicionarOutro: true,
        });
      } else {
        fecharModal();
      }
    },
    [
      adicionarProduto,
      entradaRateiaIcmsSt,
      fecharModal,
      listaVariacoes,
      onResolve,
    ]
  );

  async function submit(data: FormData, confirmarAdicionarOutro = false) {
    setIsLoading(true);
    if (produtoTipoSimples) {
      await adicionarUmProdutoSimples(data, confirmarAdicionarOutro);
      setIsLoading(false);
      return;
    }

    if (listaVariacoes?.length < 1) {
      await adicionarUmProdutoDoTipoVariacao(data, confirmarAdicionarOutro);
      setIsLoading(false);
      return;
    }

    await adicionarUmaListaProdutosDoTipoVariacao(
      data,
      confirmarAdicionarOutro
    );
    setIsLoading(false);
  }

  const handleConfirmarSair = formMethods.handleSubmit(async (data) => {
    await submit(data);
  });

  const handleConfirmarAdicionarNovo = formMethods.handleSubmit(
    async (data) => {
      await submit(data, true);
    }
  );

  const buscarTamanhoDoProduto = useCallback(async (id: string) => {
    setIsLoadingProduto(true);

    const response = await api.get<void, ResponseApi<ProdutoTamanhoProps[]>>(
      `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${id}/tamanhos`,
      { params: { status: StatusConsultaEnum.ATIVOS } }
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso && response?.dados) {
        const newSizes = response.dados
          .filter(({ padraoSistema }) => !padraoSistema)
          .map((size, index) => ({
            label: size.descricao,
            value: size.id,
            produtoCorTamanhoId: response.dados[index].id,
          }));

        setIsLoadingProduto(false);
        return newSizes;
      }
    }

    setIsLoadingProduto(false);
    return [];
  }, []);

  const buscarCoresDoProduto = useCallback(async (id: string) => {
    setIsLoadingProduto(true);

    const response = await api.get<void, ResponseApi<ProdutoCoresProps[]>>(
      `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${id}/cores`,
      { params: { status: StatusConsultaEnum.ATIVOS } }
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso && response?.dados) {
        const newColors = response.dados
          .filter(({ cor }) => !cor.padraoSistema)
          .map(({ cor }, index) => ({
            label: cor.descricao,
            value: cor.id,
            produtoCorId: response.dados[index].id,
          }));

        setIsLoadingProduto(false);
        return newColors;
      }
    }

    setIsLoadingProduto(false);
    return [];
  }, []);

  const adicionarProdutoAutomaticamente = useCallback(
    async (option: any) => {
      const { value } = option;
      const possuiVariacao =
        value.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;
      if (possuiVariacao) {
        const [cores, tamanhos] = await Promise.all([
          buscarCoresDoProduto(value.id),
          buscarTamanhoDoProduto(value.id),
        ]);

        const optionBuscadoPorCodigo = option as unknown as {
          value: {
            cor: string;
            corId: string;
            nome: string;
            produtoCorId: string;
            produtoCorTamanhoId: string;
            produtoId: string;
            tamanho: string;
            tamanhoId: string;
            tipoProduto: number;
            volumeUnitario: boolean;
            precoCompra: number;
            referencia: string;
          };
        };

        const cor = {
          value: optionBuscadoPorCodigo.value.corId,
          label: optionBuscadoPorCodigo.value.cor,
        };

        const tamanho = {
          value: optionBuscadoPorCodigo.value.tamanhoId,
          label: optionBuscadoPorCodigo.value.tamanho,
        };

        const produtoCompleto = {
          label: optionBuscadoPorCodigo.value.nome,
          value: {
            id: optionBuscadoPorCodigo.value.produtoId,
            nome: optionBuscadoPorCodigo.value.nome,
            referencia: optionBuscadoPorCodigo.value.referencia,
            tipoProduto: optionBuscadoPorCodigo.value.tipoProduto,
            volumeUnitario: optionBuscadoPorCodigo.value.volumeUnitario,
            precoCompra: optionBuscadoPorCodigo.value.precoCompra,
            coresOptions: cores,
            tamanhosOptions: tamanhos,
          },
        };

        setValue('produto', produtoCompleto);
        setValue('valorUnitario', produtoCompleto.value.precoCompra);

        setValue('cor', {
          value: cor?.value,
          label: cor?.label || '',
        });

        setValue('listaTamanhoIdQuantidade.0.tamanho', {
          value: tamanho?.value,
          label: tamanho?.label || '',
        });

        setValue('produto.value.coresOptions', cores);
        setValue('produto.value.tamanhosOptions', tamanhos);
        await handleFocus('valorUnitario');
      } else {
        setValue('produto', {
          ...option,
        });
        setValue('valorUnitario', option?.value?.precoCompra || 0);
        setValue('listaTamanhoIdQuantidade.0.tamanho', null);

        await handleFocus('listaTamanhoIdQuantidade.0.quantidade');
      }
    },
    [buscarCoresDoProduto, buscarTamanhoDoProduto, handleFocus, setValue]
  );

  const adicionarProdutoManualmente = useCallback(
    async (option: any) => {
      const { value } = option;
      const possuiVariacao =
        value.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;
      if (possuiVariacao) {
        const [cores, tamanhos] = await Promise.all([
          buscarCoresDoProduto(value.id),
          buscarTamanhoDoProduto(value.id),
        ]);

        setValue('produto', {
          ...option,
          value: {
            ...option.value,
            coresOptions: cores,
            tamanhosOptions: tamanhos,
          },
        });
        setValue('valorUnitario', option?.value?.precoCompra || 0);
        await handleFocus(
          cores?.length > 0 ? 'cor' : 'listaTamanhoIdQuantidade.0.tamanho'
        );
      } else {
        setValue('valorUnitario', option?.value?.precoCompra || 0);
        await handleFocus('listaTamanhoIdQuantidade.0.quantidade');
      }
    },
    [buscarCoresDoProduto, buscarTamanhoDoProduto, handleFocus, setValue]
  );

  const onChangeSelectProduto = useCallback(
    async (
      option: OptionType<ProdutoOptionProps> | undefined | null,
      pesquisarPorLeitor?: boolean
    ) => {
      debugger;
      setValue('valorUnitario', 0);
      limparCamposValoresFiscais();
      setListaVariacoes([]);
      resetarQuantidadeTamanho();

      if (!option) {
        return;
      }

      if (pesquisarPorLeitor) {
        await adicionarProdutoAutomaticamente(option);
      } else {
        await adicionarProdutoManualmente(option);
      }
    },
    [
      setValue,
      limparCamposValoresFiscais,
      resetarQuantidadeTamanho,
      adicionarProdutoAutomaticamente,
      adicionarProdutoManualmente,
    ]
  );

  const buscarProdutoPorCodigo = useCallback(
    async (codigo: string) => {
      setIsLoadingProduto(true);

      const response = await api.get<
        void,
        ResponseApi<
          {
            cor: string;
            corId: string;
            nome: string;
            produtoCorId: string;
            produtoCorTamanhoId: string;
            produtoId: string;
            tamanho: string;
            tamanhoId: string;
            tipoProduto: number;
            volumeUnitario: boolean;
            precoCompra: number;
            referencia: string;
          }[]
        >
      >(ConstanteEnderecoWebservice.LISTAR_SELECT_PRODUTO_CODIGO, {
        params: { codigoProduto: codigo },
      });

      if (response) {
        if (response?.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }
        setTotalRegistros(response.dados?.length || 0);

        if (response?.sucesso && response?.dados) {
          const data = response.dados?.map((produto) => ({
            label: produto.nome,
            value: {
              ...produto,
              referencia: produto.referencia || '',
              tipoProduto: produto.tipoProduto,
              volumeUnitario: produto.volumeUnitario,
              precoCompra: produto.precoCompra,
              id: produto.produtoId,
              coresOptions: produto.cor
                ? [
                    {
                      label: produto.cor,
                      value: produto.produtoCorId,
                    },
                  ]
                : [],
              tamanhosOptions: produto.tamanho
                ? [
                    {
                      label: produto.tamanho,
                      value: produto.produtoCorTamanhoId,
                    },
                  ]
                : [],
            },
          }));
          if (data.length === 1) {
            const produto = data[0];
            await onChangeSelectProduto(produto, true);
            setIsLoadingProduto(false);
            const { value } = produto;
            const possuiVariacao =
              value.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;
            if (possuiVariacao) {
              await handleFocus('valorUnitario');
            } else {
              await handleFocus('listaTamanhoIdQuantidade.0.quantidade');
            }
            return [];
          }

          setIsLoadingProduto(false);
          return data;
        }
      }

      setIsLoadingProduto(false);
      return [];
    },
    [handleFocus, onChangeSelectProduto]
  );

  const buscarProdutoDigitando = useCallback(
    async (
      valorDigitado: string,
      dataPagination: PaginationData
    ): Promise<OptionType<ProdutoOptionProps>[]> => {
      setIsLoadingProduto(true);
      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<ProdutoOptionProps>>
      >(
        formatQueryPagegTable(
          ConstanteEnderecoWebservice.PRODUTO_COR_TAMANHO_LISTAR_SELECT_ENTRADA_MERCADORIA,
          {
            ...dataPagination,
            orderColumn: 'Nome',
            orderDirection: 'asc',
          }
        ),
        {
          params: { nomeSkuCodigoExternoBarrasGtinEan: valorDigitado },
        }
      );

      if (response) {
        if (response?.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }
        setTotalRegistros(response.dados.total || 0);

        if (response?.sucesso && response?.dados) {
          const data = response.dados?.registros.map((produto) => ({
            label: produto.nome,
            value: { ...produto, coresOptions: [], tamanhosOptions: [] },
          }));

          setIsLoadingProduto(false);
          return data;
        }
      }

      setIsLoadingProduto(false);
      return [];
    },
    []
  );

  const validarPesquisaPorCodigo = useCallback(
    (pesquisa: string) => {
      const pesquisaVazia = pesquisa.trim() === '';
      if (pesquisaVazia) {
        return false;
      }
      const pesquisaPorCodigo =
        checarPesquisaPorCodigoBarras(pesquisa) ||
        checarPesquisaPorSKU(pesquisa) ||
        checarPesquisaPorGtinEan(pesquisa);

      if (!pesquisaPorCodigo) {
        formMethods.setValue('pesquisarPorLeitor', false);
      } else {
        formMethods.setValue('pesquisarPorLeitor', true);
      }

      return pesquisaPorCodigo;
    },
    [formMethods]
  );

  const buscarProduto = useCallback(
    async (
      valorPesquisado: string,
      dataPagination: PaginationData,
      pesquisarPorLeitor?: boolean
    ): Promise<OptionType<ProdutoOptionProps>[]> => {
      limparCampoProduto();

      const valorPesquisadoPorCodigo =
        validarPesquisaPorCodigo(valorPesquisado);

      if (pesquisarPorLeitor || valorPesquisadoPorCodigo) {
        return buscarProdutoPorCodigo(valorPesquisado);
      }

      return buscarProdutoDigitando(valorPesquisado, dataPagination);
    },
    [
      buscarProdutoDigitando,
      buscarProdutoPorCodigo,
      limparCampoProduto,
      validarPesquisaPorCodigo,
    ]
  );

  const cadastrarOpcaoNova = useCallback(
    async (
      inputValue: string,
      tipoCadastro: TipoCadastro,
      temPermissao: boolean,
      produtoId: string
    ) => {
      if (temPermissao) {
        const modalResponse =
          tipoCadastro === 'cor'
            ? await ModalCadastrarCor({ inputValue, produtoId })
            : await ModalCadastrarTamanho({ inputValue, produtoId });

        let newOption: NewOption | undefined;

        if (tipoCadastro === 'cor' && 'cor' in modalResponse) {
          const { cor } = modalResponse;
          if (cor) {
            newOption = {
              id: cor.corId,
              descricao: cor.corDescricao,
            };
          }
        } else if (tipoCadastro === 'tamanho' && 'tamanho' in modalResponse) {
          const { tamanho } = modalResponse;
          if (tamanho) {
            newOption = {
              id: tamanho.id,
              descricao: tamanho.descricao,
            };
          }
        }

        if (newOption) {
          const option = {
            value: newOption.id,
            label: newOption.descricao,
          };
          return option;
        }
      }

      toast.warning(
        'Você não tem permissão para acessar essa função. Consulte o administrador da conta.'
      );

      return undefined;
    },
    []
  );

  async function handleCadastrarCor(inputValue: string) {
    const coresAtuais = produtoWatch?.value?.coresOptions || [];
    const novaCor = await cadastrarOpcaoNova(
      inputValue,
      'cor',
      temPermissaoCadastrarCor,
      produtoWatch?.value.id || ''
    );
    if (novaCor) {
      setValue('produto.value.coresOptions', [...coresAtuais, novaCor]);
    }
    return novaCor;
  }

  async function handleCadastrarTamanho(inputValue: string) {
    const tamanhosAtuais = produtoWatch?.value?.tamanhosOptions || [];
    const novoTamanho = await cadastrarOpcaoNova(
      inputValue,
      'tamanho',
      temPermissaoCadastrarTamanho,
      produtoWatch?.value.id || ''
    );
    if (novoTamanho) {
      setValue('produto.value.tamanhosOptions', [
        ...tamanhosAtuais,
        novoTamanho,
      ]);
    }
    return novoTamanho;
  }

  const cadastrarNovoProduto = async (inputValue: string) => {
    const { id: idLojaAtual } = auth.getLoja();
    const { produto: produtoCadastrado } = await ModalCadastrarProduto({
      inputValue,
    });

    let precoCompra = 0;
    const precoCompraProduto: Lojas[] =
      produtoCadastrado?.produtoPrecoLojas?.filter(
        (loja: Lojas) => loja.lojaId === idLojaAtual
      );

    if (precoCompraProduto.length > 0) {
      const precoCompraLojaAtual = precoCompraProduto[0].precoCompra;
      precoCompra = Number(precoCompraLojaAtual);
    } else {
      precoCompra = 0;
    }

    const routeApi = `${ConstanteEnderecoWebservice.PRODUTO_COR_TAMANHO_OBTER_OPCAO_SELECT_ENTRADA_MERCADORIA}?produtoId=${produtoCadastrado.id}`;
    const response = await api.get<void, ResponseApi<ProdutoOptionProps>>(
      routeApi
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso && response?.dados) {
        const produtoTemVariacoes =
          response.dados.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;

        const newProduct = {
          label: response.dados.nome,
          value: {
            ...response.dados,
            coresOptions: [],
            tamanhosOptions: [],
          },
        };

        if (!produtoTemVariacoes) {
          setIsLoadingProduto(false);

          return {
            ...newProduct,
            value: {
              ...newProduct.value,
              precoCompra,
            },
          };
        }

        const [cores, tamanhos] = await Promise.all([
          buscarCoresDoProduto(response.dados.id),
          buscarTamanhoDoProduto(response.dados.id),
        ]);

        setIsLoadingProduto(false);

        return {
          ...newProduct,
          value: {
            ...newProduct.value,
            coresOptions: cores,
            tamanhosOptions: tamanhos,
            precoCompra,
          },
        };
      }
    }

    setIsLoadingProduto(false);
    return undefined;
  };

  const handleCadastrarProduto = async (inputValue: string) => {
    if (!temPermissaoCadastrarProduto) {
      toast.warning('Você não possui permissão para cadastrar produtos');
      return undefined;
    }
    const produto = await cadastrarNovoProduto(inputValue);
    const precoCompra = produto?.value?.precoCompra || 0;
    if (precoCompra) {
      setValue('valorUnitario', precoCompra);
    }
    if (produto) {
      await onChangeSelectProduto(produto, true);
    }
    return produto;
  };

  const abriModalVariacoes = useCallback(async () => {
    const dadosVariacoes = await ModalCriarVariacao({
      produtoId: produtoSelecionado?.id || '',
    });

    if (dadosVariacoes?.sucesso) {
      setValue('produto.value.coresOptions', dadosVariacoes?.listaCores);
      setValue('produto.value.tamanhosOptions', dadosVariacoes?.listaTamanhos);
    }
  }, [setValue, produtoSelecionado]);

  return {
    buscarProduto,
    onChangeSelectProduto,
    handleCadastrarProduto,
    handleCadastrarTamanho,
    handleConfirmarAdicionarNovo,
    handleConfirmarSair,
    handleCadastrarCor,
    handleAbrirModalEscolherGradeTamanhos,
    adicionarVariacaoNaLista,
    setListaVariacoes,
    quantidadeMaiorQueZero,
    abriModalVariacoes,
    isLoadingProduto,
    isLoading,
    listaTamanhoIdQuantidade,
    limparValoresFiscaisVoltarValorPrecoCompra,
    listaVariacoes,
    totalRegistros,
    produtoSelecionado,
    produtoTemCores,
    produtoDeVolumeUnitario,
    produtoTipoSimples,
    produtoTipoVariacao,
    produtoTemTamanhos,
    coresDoProduto,
    tamanhosDoProduto,
    podeConfirmar,
    corEscolhida,
    temGradeLancada,
    validarPesquisaPorCodigo,
    pesquisarPorLeitorWatch,
    setValue,
  };
};
